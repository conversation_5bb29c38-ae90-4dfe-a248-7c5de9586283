/**
 * CanvasTimelineRenderer - High-performance Canvas-based timeline renderer
 * Replaces DOM-based timeline items to improve performance with large datasets
 */

export class CanvasTimelineRenderer {
  constructor(container, options = {}) {
    this.container = container
    this.options = {
      trackHeight: 80,
      itemHeight: 70,
      itemPadding: 5,
      bufferTime: 10, // seconds to render outside viewport
      pixelsPerSecond: 100,
      zoom: 1,
      ...options
    }

    // Canvas layers
    this.backgroundCanvas = null
    this.itemsCanvas = null
    this.overlayCanvas = null
    
    // Contexts
    this.backgroundCtx = null
    this.itemsCtx = null
    this.overlayCtx = null

    // State
    this.items = []
    this.selectedItems = new Set()
    this.hoveredItem = null
    this.dragState = null
    this.viewportRange = { start: 0, end: 0 }
    this.dirtyRegions = new Set()
    
    // Performance tracking
    this.lastRenderTime = 0
    this.renderCount = 0

    this.init()
  }

  init() {
    this.createCanvasLayers()
    this.setupEventListeners()
    this.updateViewport()
  }

  createCanvasLayers() {
    const { trackHeight } = this.options
    
    // Background layer (grid, static elements)
    this.backgroundCanvas = document.createElement('canvas')
    this.backgroundCanvas.className = 'timeline-canvas-background absolute inset-0'
    this.backgroundCanvas.style.zIndex = '1'
    this.backgroundCtx = this.backgroundCanvas.getContext('2d')

    // Items layer (timeline items, waveforms)
    this.itemsCanvas = document.createElement('canvas')
    this.itemsCanvas.className = 'timeline-canvas-items absolute inset-0'
    this.itemsCanvas.style.zIndex = '2'
    this.itemsCtx = this.itemsCanvas.getContext('2d')

    // Overlay layer (drag handles, tooltips)
    this.overlayCanvas = document.createElement('canvas')
    this.overlayCanvas.className = 'timeline-canvas-overlay absolute inset-0'
    this.overlayCanvas.style.zIndex = '3'
    this.overlayCtx = this.overlayCanvas.getContext('2d')

    // Set canvas dimensions
    this.updateCanvasSize()

    // Add to container
    this.container.appendChild(this.backgroundCanvas)
    this.container.appendChild(this.itemsCanvas)
    this.container.appendChild(this.overlayCanvas)
  }

  updateCanvasSize() {
    const rect = this.container.getBoundingClientRect()
    const width = rect.width
    const height = this.options.trackHeight

    [this.backgroundCanvas, this.itemsCanvas, this.overlayCanvas].forEach(canvas => {
      // Set actual canvas size (for high DPI)
      const dpr = window.devicePixelRatio || 1
      canvas.width = width * dpr
      canvas.height = height * dpr
      
      // Set CSS size
      canvas.style.width = width + 'px'
      canvas.style.height = height + 'px'
      
      // Scale context for high DPI
      const ctx = canvas.getContext('2d')
      ctx.scale(dpr, dpr)
    })

    this.canvasWidth = width
    this.canvasHeight = height
  }

  setupEventListeners() {
    // Single event listener on overlay canvas for all interactions
    this.overlayCanvas.addEventListener('mousedown', this.handleMouseDown.bind(this))
    this.overlayCanvas.addEventListener('mousemove', this.handleMouseMove.bind(this))
    this.overlayCanvas.addEventListener('mouseup', this.handleMouseUp.bind(this))
    this.overlayCanvas.addEventListener('contextmenu', this.handleContextMenu.bind(this))
    this.overlayCanvas.addEventListener('wheel', this.handleWheel.bind(this))

    // Resize observer
    this.resizeObserver = new ResizeObserver(() => {
      this.updateCanvasSize()
      this.markDirty('all')
      this.render()
    })
    this.resizeObserver.observe(this.container)
  }

  // Coordinate conversion
  timeToPixel(time) {
    return time * this.options.pixelsPerSecond * this.options.zoom
  }

  pixelToTime(pixel) {
    return pixel / (this.options.pixelsPerSecond * this.options.zoom)
  }

  // Viewport management
  updateViewport(scrollLeft = 0) {
    const viewportWidth = this.canvasWidth
    const startTime = this.pixelToTime(scrollLeft) - this.options.bufferTime
    const endTime = this.pixelToTime(scrollLeft + viewportWidth) + this.options.bufferTime

    this.viewportRange = { start: Math.max(0, startTime), end: endTime }
    this.markDirty('items')
  }

  // Get items visible in current viewport with advanced culling
  getVisibleItems() {
    // Early return if no items
    if (!this.items.length) return []

    // Use binary search for better performance with large datasets
    const startIndex = this.findItemIndex(this.viewportRange.start, 'start')
    const endIndex = this.findItemIndex(this.viewportRange.end, 'end')

    // Return slice of items in visible range
    return this.items.slice(startIndex, endIndex + 1).filter(item =>
      item.endTime >= this.viewportRange.start &&
      item.startTime <= this.viewportRange.end
    )
  }

  // Binary search to find item index for viewport culling
  findItemIndex(time, type) {
    let left = 0
    let right = this.items.length - 1

    while (left <= right) {
      const mid = Math.floor((left + right) / 2)
      const item = this.items[mid]
      const compareTime = type === 'start' ? item.startTime : item.endTime

      if (compareTime < time) {
        left = mid + 1
      } else {
        right = mid - 1
      }
    }

    return type === 'start' ? Math.max(0, right) : Math.min(this.items.length - 1, left)
  }

  // Optimized visible items with spatial indexing
  getVisibleItemsOptimized() {
    // For very large datasets (>5000 items), use spatial indexing
    if (this.items.length > 5000 && !this.spatialIndex) {
      this.buildSpatialIndex()
    }

    if (this.spatialIndex) {
      return this.spatialIndex.query(this.viewportRange.start, this.viewportRange.end)
    }

    return this.getVisibleItems()
  }

  // Build spatial index for very large datasets
  buildSpatialIndex() {
    // Simple grid-based spatial index
    const gridSize = 10 // seconds per grid cell
    this.spatialIndex = {
      grid: new Map(),
      gridSize,

      add: function(item) {
        const startGrid = Math.floor(item.startTime / gridSize)
        const endGrid = Math.floor(item.endTime / gridSize)

        for (let i = startGrid; i <= endGrid; i++) {
          if (!this.grid.has(i)) {
            this.grid.set(i, [])
          }
          this.grid.get(i).push(item)
        }
      },

      query: function(startTime, endTime) {
        const startGrid = Math.floor(startTime / gridSize)
        const endGrid = Math.floor(endTime / gridSize)
        const results = new Set()

        for (let i = startGrid; i <= endGrid; i++) {
          const gridItems = this.grid.get(i) || []
          gridItems.forEach(item => {
            if (item.endTime >= startTime && item.startTime <= endTime) {
              results.add(item)
            }
          })
        }

        return Array.from(results)
      }
    }

    // Populate spatial index
    this.items.forEach(item => this.spatialIndex.add(item))
  }

  // Invalidate spatial index when items change
  invalidateSpatialIndex() {
    this.spatialIndex = null
  }

  // Hit testing - find item at coordinates
  hitTest(x, y) {
    const time = this.pixelToTime(x)
    const visibleItems = this.getVisibleItems()
    
    // Check from top to bottom (reverse order for z-index)
    for (let i = visibleItems.length - 1; i >= 0; i--) {
      const item = visibleItems[i]
      if (time >= item.startTime && time <= item.endTime) {
        const itemX = this.timeToPixel(item.startTime)
        const itemWidth = this.timeToPixel(item.endTime - item.startTime)
        
        if (x >= itemX && x <= itemX + itemWidth && 
            y >= this.options.itemPadding && 
            y <= this.options.itemHeight + this.options.itemPadding) {
          return {
            item,
            region: this.getHitRegion(item, x, y)
          }
        }
      }
    }
    return null
  }

  getHitRegion(item, x, y) {
    const itemX = this.timeToPixel(item.startTime)
    const itemWidth = this.timeToPixel(item.endTime - item.startTime)
    const relativeX = x - itemX

    // Check resize handles
    if (relativeX <= 8) return 'resize-left'
    if (relativeX >= itemWidth - 8) return 'resize-right'
    
    // Check audio controls area (top-right)
    if (relativeX >= itemWidth - 60 && y <= 25) return 'audio-controls'
    
    return 'body'
  }

  // Dirty region management
  markDirty(region) {
    this.dirtyRegions.add(region)
  }

  clearDirty() {
    this.dirtyRegions.clear()
  }

  // Main render method
  render() {
    const startTime = performance.now()

    if (this.dirtyRegions.has('all') || this.dirtyRegions.has('background')) {
      this.renderBackground()
    }

    if (this.dirtyRegions.has('all') || this.dirtyRegions.has('items')) {
      this.renderItems()
    }

    if (this.dirtyRegions.has('all') || this.dirtyRegions.has('overlay')) {
      this.renderOverlay()
    }

    this.clearDirty()
    
    // Performance tracking
    this.lastRenderTime = performance.now() - startTime
    this.renderCount++
  }

  renderBackground() {
    const ctx = this.backgroundCtx
    ctx.clearRect(0, 0, this.canvasWidth, this.canvasHeight)

    // Render grid lines
    this.renderGrid(ctx)
  }

  renderGrid(ctx) {
    const { zoom, pixelsPerSecond } = this.options
    const gridInterval = 1 // 1 second intervals
    const gridSpacing = gridInterval * pixelsPerSecond * zoom

    ctx.strokeStyle = 'rgba(255, 255, 255, 0.1)'
    ctx.lineWidth = 1

    ctx.beginPath()
    for (let x = 0; x < this.canvasWidth; x += gridSpacing) {
      ctx.moveTo(x, 0)
      ctx.lineTo(x, this.canvasHeight)
    }
    ctx.stroke()
  }

  renderItems() {
    const ctx = this.itemsCtx
    ctx.clearRect(0, 0, this.canvasWidth, this.canvasHeight)

    // Use optimized visible items for better performance
    const visibleItems = this.getVisibleItemsOptimized()

    // Performance tracking
    const renderStart = performance.now()

    // Batch render items for better performance
    visibleItems.forEach(item => {
      this.renderItem(ctx, item)
    })

    // Update performance stats
    this.lastItemRenderTime = performance.now() - renderStart
    this.lastVisibleItemCount = visibleItems.length

    // Debug info (can be removed in production)
    if (this.options.debug) {
      console.log(`Rendered ${visibleItems.length}/${this.items.length} items in ${this.lastItemRenderTime.toFixed(2)}ms`)
    }
  }

  renderItem(ctx, item) {
    const x = this.timeToPixel(item.startTime)
    const width = this.timeToPixel(item.endTime - item.startTime)
    const y = this.options.itemPadding
    const height = this.options.itemHeight

    // Item background
    const isSelected = this.selectedItems.has(item.id)
    const isHovered = this.hoveredItem === item.id

    ctx.fillStyle = this.getItemColor(item, isSelected, isHovered)
    ctx.fillRect(x, y, width, height)

    // Item border
    ctx.strokeStyle = this.getItemBorderColor(item, isSelected, isHovered)
    ctx.lineWidth = isSelected ? 2 : 1
    ctx.strokeRect(x, y, width, height)

    // Item text
    this.renderItemText(ctx, item, x, y, width, height)

    // Audio indicators
    if (item.hasAudio) {
      this.renderAudioIndicators(ctx, item, x, y, width, height)
    }
  }

  getItemColor(item, isSelected, isHovered) {
    if (isSelected) return '#3b82f6'
    if (isHovered) return '#4b5563'
    return '#374151'
  }

  getItemBorderColor(item, isSelected, isHovered) {
    if (isSelected) return '#60a5fa'
    if (isHovered) return '#6b7280'
    return '#4b5563'
  }

  renderItemText(ctx, item, x, y, width, height) {
    ctx.fillStyle = '#ffffff'
    ctx.font = '12px Inter, sans-serif'
    ctx.textAlign = 'left'
    ctx.textBaseline = 'top'

    // Item ID
    ctx.fillText(item.id, x + 8, y + 8)

    // Item text (truncated)
    const text = item.translatedText || item.text || ''
    const maxWidth = width - 16
    const truncatedText = this.truncateText(ctx, text, maxWidth)
    
    ctx.font = '11px Inter, sans-serif'
    ctx.fillStyle = '#d1d5db'
    ctx.fillText(truncatedText, x + 8, y + 25)
  }

  truncateText(ctx, text, maxWidth) {
    if (ctx.measureText(text).width <= maxWidth) return text
    
    let truncated = text
    while (ctx.measureText(truncated + '...').width > maxWidth && truncated.length > 0) {
      truncated = truncated.slice(0, -1)
    }
    return truncated + '...'
  }

  renderAudioIndicators(ctx, item, x, y, width, height) {
    // Render waveform background if available
    if (item.waveformData && width > 50) {
      this.renderWaveform(ctx, item, x, y, width, height)
    }

    // Audio play button with better styling
    const buttonX = x + width - 30
    const buttonY = y + 8
    const buttonSize = 16
    const isPlaying = item.isPlaying || false

    // Button background with rounded corners
    ctx.fillStyle = isPlaying ? '#ef4444' : '#3b82f6'
    this.roundRect(ctx, buttonX, buttonY, buttonSize, buttonSize, 3)
    ctx.fill()

    // Button icon
    ctx.fillStyle = '#ffffff'
    if (isPlaying) {
      // Pause icon
      ctx.fillRect(buttonX + 4, buttonY + 3, 2, 10)
      ctx.fillRect(buttonX + 10, buttonY + 3, 2, 10)
    } else {
      // Play icon
      ctx.beginPath()
      ctx.moveTo(buttonX + 5, buttonY + 3)
      ctx.lineTo(buttonX + 5, buttonY + 13)
      ctx.lineTo(buttonX + 12, buttonY + 8)
      ctx.closePath()
      ctx.fill()
    }

    // Audio duration indicator
    if (item.audioDuration) {
      ctx.fillStyle = '#9ca3af'
      ctx.font = '10px Inter, sans-serif'
      ctx.textAlign = 'right'
      const durationText = this.formatDuration(item.audioDuration)
      ctx.fillText(durationText, x + width - 35, buttonY + 12)
    }

    // Voice generation indicators with better styling
    this.renderVoiceIndicators(ctx, item, x, y, width, height)

    // Audio progress bar if playing
    if (isPlaying && item.audioProgress !== undefined) {
      this.renderAudioProgress(ctx, item, x, y, width, height)
    }
  }

  renderWaveform(ctx, item, x, y, width, height) {
    if (!item.waveformData || width < 20) return

    const waveformHeight = height * 0.3
    const waveformY = y + height - waveformHeight - 15

    ctx.save()
    ctx.globalAlpha = 0.3
    ctx.fillStyle = '#60a5fa'

    const data = item.waveformData
    const samplesPerPixel = Math.max(1, Math.floor(data.length / width))

    for (let i = 0; i < width; i++) {
      const sampleIndex = Math.floor(i * samplesPerPixel)
      if (sampleIndex < data.length) {
        const amplitude = Math.abs(data[sampleIndex])
        const barHeight = amplitude * waveformHeight
        ctx.fillRect(x + i, waveformY + (waveformHeight - barHeight) / 2, 1, barHeight)
      }
    }

    ctx.restore()
  }

  renderVoiceIndicators(ctx, item, x, y, width, height) {
    let indicatorX = x + 8
    const indicatorY = y + height - 12
    const indicatorSize = 6
    const spacing = 8

    const voices = [
      { key: 'isGenerated1', color: '#10b981', label: '1' },
      { key: 'isGenerated2', color: '#3b82f6', label: '2' },
      { key: 'isGenerated3', color: '#8b5cf6', label: '3' },
      { key: 'isGenerated4', color: '#f59e0b', label: '4' },
      { key: 'isGenerated5', color: '#ef4444', label: '5' }
    ]

    voices.forEach(voice => {
      if (item[voice.key]) {
        // Voice indicator circle
        ctx.fillStyle = voice.color
        ctx.beginPath()
        ctx.arc(indicatorX + indicatorSize/2, indicatorY + indicatorSize/2, indicatorSize/2, 0, Math.PI * 2)
        ctx.fill()

        // Voice number
        ctx.fillStyle = '#ffffff'
        ctx.font = '8px Inter, sans-serif'
        ctx.textAlign = 'center'
        ctx.textBaseline = 'middle'
        ctx.fillText(voice.label, indicatorX + indicatorSize/2, indicatorY + indicatorSize/2)

        indicatorX += spacing
      }
    })
  }

  renderAudioProgress(ctx, item, x, y, width, height) {
    const progressY = y + height - 3
    const progressHeight = 2
    const progress = Math.max(0, Math.min(1, item.audioProgress))

    // Progress background
    ctx.fillStyle = 'rgba(255, 255, 255, 0.2)'
    ctx.fillRect(x, progressY, width, progressHeight)

    // Progress fill
    ctx.fillStyle = '#3b82f6'
    ctx.fillRect(x, progressY, width * progress, progressHeight)
  }

  // Helper method for rounded rectangles
  roundRect(ctx, x, y, width, height, radius) {
    ctx.beginPath()
    ctx.moveTo(x + radius, y)
    ctx.lineTo(x + width - radius, y)
    ctx.quadraticCurveTo(x + width, y, x + width, y + radius)
    ctx.lineTo(x + width, y + height - radius)
    ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height)
    ctx.lineTo(x + radius, y + height)
    ctx.quadraticCurveTo(x, y + height, x, y + height - radius)
    ctx.lineTo(x, y + radius)
    ctx.quadraticCurveTo(x, y, x + radius, y)
    ctx.closePath()
  }

  // Format duration helper
  formatDuration(seconds) {
    if (seconds < 60) {
      return `${seconds.toFixed(1)}s`
    }
    const mins = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  renderOverlay() {
    const ctx = this.overlayCtx
    ctx.clearRect(0, 0, this.canvasWidth, this.canvasHeight)

    // Render resize handles for selected items
    this.selectedItems.forEach(itemId => {
      const item = this.items.find(i => i.id === itemId)
      if (item && this.isItemVisible(item)) {
        this.renderResizeHandles(ctx, item)
      }
    })

    // Render drag preview
    if (this.dragState) {
      this.renderDragPreview(ctx)
    }
  }

  renderResizeHandles(ctx, item) {
    const x = this.timeToPixel(item.startTime)
    const width = this.timeToPixel(item.endTime - item.startTime)
    const y = this.options.itemPadding
    const height = this.options.itemHeight

    ctx.fillStyle = '#3b82f6'
    
    // Left handle
    ctx.fillRect(x - 2, y, 4, height)
    
    // Right handle
    ctx.fillRect(x + width - 2, y, 4, height)
  }

  isItemVisible(item) {
    return item.endTime >= this.viewportRange.start && 
           item.startTime <= this.viewportRange.end
  }

  renderDragPreview(ctx) {
    // Implementation for drag preview rendering
    // Will be implemented in event handling section
  }

  // Public API methods
  setItems(items) {
    this.items = items

    // Sort items by start time for better viewport culling performance
    this.items.sort((a, b) => a.startTime - b.startTime)

    // Invalidate spatial index when items change
    this.invalidateSpatialIndex()

    this.markDirty('items')
  }

  setSelectedItems(selectedIds) {
    this.selectedItems = new Set(selectedIds)
    this.markDirty('overlay')
  }

  setZoom(zoom) {
    this.options.zoom = zoom
    this.markDirty('all')
  }

  setScrollPosition(scrollLeft) {
    this.updateViewport(scrollLeft)
    this.render()
  }

  destroy() {
    if (this.resizeObserver) {
      this.resizeObserver.disconnect()
    }
    
    // Remove canvases
    [this.backgroundCanvas, this.itemsCanvas, this.overlayCanvas].forEach(canvas => {
      if (canvas && canvas.parentNode) {
        canvas.parentNode.removeChild(canvas)
      }
    })
  }

  // Event handlers
  handleMouseDown(event) {
    event.preventDefault()

    const rect = this.overlayCanvas.getBoundingClientRect()
    const x = event.clientX - rect.left
    const y = event.clientY - rect.top

    const hit = this.hitTest(x, y)

    if (hit) {
      const { item, region } = hit

      // Handle different regions
      switch (region) {
        case 'resize-left':
          this.startResize(item, 'left', event)
          break
        case 'resize-right':
          this.startResize(item, 'right', event)
          break
        case 'audio-controls':
          this.handleAudioControl(item, x, y)
          break
        case 'body':
          this.startDrag(item, event)
          break
      }
    } else {
      // Click on empty space - clear selection
      this.clearSelection()
    }

    // Add global mouse listeners for drag operations
    document.addEventListener('mousemove', this.handleGlobalMouseMove.bind(this))
    document.addEventListener('mouseup', this.handleGlobalMouseUp.bind(this))
  }

  handleMouseMove(event) {
    const rect = this.overlayCanvas.getBoundingClientRect()
    const x = event.clientX - rect.left
    const y = event.clientY - rect.top

    const hit = this.hitTest(x, y)

    // Update hover state
    const newHoveredItem = hit ? hit.item.id : null
    if (newHoveredItem !== this.hoveredItem) {
      this.hoveredItem = newHoveredItem
      this.markDirty('items')
      this.render()
    }

    // Update cursor
    this.updateCursor(hit)
  }

  handleMouseUp(event) {
    // Mouse up is handled by global listener
  }

  handleGlobalMouseMove(event) {
    if (!this.dragState) return

    const rect = this.overlayCanvas.getBoundingClientRect()
    const x = event.clientX - rect.left
    const y = event.clientY - rect.top

    this.updateDrag(x, y)
  }

  handleGlobalMouseUp(event) {
    if (this.dragState) {
      this.endDrag()
    }

    // Remove global listeners
    document.removeEventListener('mousemove', this.handleGlobalMouseMove.bind(this))
    document.removeEventListener('mouseup', this.handleGlobalMouseUp.bind(this))
  }

  handleContextMenu(event) {
    event.preventDefault()

    const rect = this.overlayCanvas.getBoundingClientRect()
    const x = event.clientX - rect.left
    const y = event.clientY - rect.top

    const hit = this.hitTest(x, y)
    const time = this.pixelToTime(x)

    // Emit context menu event
    this.emit('context-menu', {
      event,
      item: hit ? hit.item : null,
      time,
      position: { x: event.clientX, y: event.clientY }
    })
  }

  handleWheel(event) {
    // Let parent handle wheel events (zoom, scroll)
    // This allows the existing wheel handling in TimelineEditor to work
  }

  // Drag operations
  startDrag(item, event) {
    // Select item if not already selected
    if (!this.selectedItems.has(item.id)) {
      if (!event.ctrlKey && !event.metaKey) {
        this.selectedItems.clear()
      }
      this.selectedItems.add(item.id)
      this.markDirty('overlay')
    }

    const rect = this.overlayCanvas.getBoundingClientRect()
    const startX = event.clientX - rect.left

    this.dragState = {
      type: 'move',
      startX,
      startTime: this.pixelToTime(startX),
      items: Array.from(this.selectedItems).map(id => {
        const dragItem = this.items.find(i => i.id === id)
        return {
          id,
          originalStartTime: dragItem.startTime,
          originalEndTime: dragItem.endTime
        }
      })
    }

    this.emit('drag-start', { items: this.dragState.items })
  }

  startResize(item, direction, event) {
    // Select item if not selected
    if (!this.selectedItems.has(item.id)) {
      this.selectedItems.clear()
      this.selectedItems.add(item.id)
      this.markDirty('overlay')
    }

    const rect = this.overlayCanvas.getBoundingClientRect()
    const startX = event.clientX - rect.left

    this.dragState = {
      type: `resize-${direction}`,
      startX,
      startTime: this.pixelToTime(startX),
      item: {
        id: item.id,
        originalStartTime: item.startTime,
        originalEndTime: item.endTime
      }
    }

    this.emit('resize-start', { item: this.dragState.item, direction })
  }

  updateDrag(x, y) {
    if (!this.dragState) return

    const currentTime = this.pixelToTime(x)
    const deltaTime = currentTime - this.dragState.startTime

    if (this.dragState.type === 'move') {
      // Update all selected items
      this.dragState.items.forEach(dragItem => {
        const item = this.items.find(i => i.id === dragItem.id)
        if (item) {
          const newStartTime = Math.max(0, dragItem.originalStartTime + deltaTime)
          const duration = dragItem.originalEndTime - dragItem.originalStartTime

          item.startTime = newStartTime
          item.endTime = newStartTime + duration
        }
      })

      this.emit('drag', { items: this.dragState.items, deltaTime })
    } else if (this.dragState.type.startsWith('resize-')) {
      const item = this.items.find(i => i.id === this.dragState.item.id)
      if (item) {
        if (this.dragState.type === 'resize-left') {
          item.startTime = Math.max(0, Math.min(
            this.dragState.item.originalStartTime + deltaTime,
            this.dragState.item.originalEndTime - 0.1 // Minimum duration
          ))
        } else {
          item.endTime = Math.max(
            this.dragState.item.originalStartTime + 0.1, // Minimum duration
            this.dragState.item.originalEndTime + deltaTime
          )
        }
      }

      this.emit('resize', { item: this.dragState.item, deltaTime })
    }

    this.markDirty('items')
    this.render()
  }

  endDrag() {
    if (!this.dragState) return

    if (this.dragState.type === 'move') {
      this.emit('drag-end', { items: this.dragState.items })
    } else if (this.dragState.type.startsWith('resize-')) {
      this.emit('resize-end', { item: this.dragState.item })
    }

    this.dragState = null
    this.markDirty('overlay')
    this.render()
  }

  // Audio control handling
  handleAudioControl(item, x, y) {
    const itemX = this.timeToPixel(item.startTime)
    const itemWidth = this.timeToPixel(item.endTime - item.startTime)
    const relativeX = x - itemX
    const relativeY = y - this.options.itemPadding

    // Check if click is on play button
    const buttonX = itemWidth - 30
    const buttonY = 8

    if (relativeX >= buttonX && relativeX <= buttonX + 16 &&
        relativeY >= buttonY && relativeY <= buttonY + 16) {
      this.emit('audio-toggle', { item })
      return
    }

    // Check if click is on waveform for seeking
    if (item.waveformData && relativeY >= this.options.itemHeight - 20 && relativeY <= this.options.itemHeight - 5) {
      const seekProgress = Math.max(0, Math.min(1, relativeX / itemWidth))
      this.emit('audio-seek', { item, progress: seekProgress })
      return
    }

    // Check if click is on voice indicators for voice selection
    const indicatorY = this.options.itemHeight - 12
    if (relativeY >= indicatorY && relativeY <= indicatorY + 6) {
      let indicatorX = 8
      const voices = ['isGenerated1', 'isGenerated2', 'isGenerated3', 'isGenerated4', 'isGenerated5']

      for (let i = 0; i < voices.length; i++) {
        if (item[voices[i]] && relativeX >= indicatorX && relativeX <= indicatorX + 6) {
          this.emit('voice-select', { item, voiceIndex: i + 1 })
          return
        }
        if (item[voices[i]]) {
          indicatorX += 8
        }
      }
    }
  }

  // Selection management
  clearSelection() {
    if (this.selectedItems.size > 0) {
      this.selectedItems.clear()
      this.markDirty('overlay')
      this.render()
      this.emit('selection-changed', { selectedItems: [] })
    }
  }

  selectItem(itemId, addToSelection = false) {
    if (!addToSelection) {
      this.selectedItems.clear()
    }
    this.selectedItems.add(itemId)
    this.markDirty('overlay')
    this.render()
    this.emit('selection-changed', { selectedItems: Array.from(this.selectedItems) })
  }

  // Cursor management
  updateCursor(hit) {
    if (!hit) {
      this.overlayCanvas.style.cursor = 'default'
      return
    }

    switch (hit.region) {
      case 'resize-left':
      case 'resize-right':
        this.overlayCanvas.style.cursor = 'ew-resize'
        break
      case 'audio-controls':
        this.overlayCanvas.style.cursor = 'pointer'
        break
      case 'body':
        this.overlayCanvas.style.cursor = 'move'
        break
      default:
        this.overlayCanvas.style.cursor = 'default'
    }
  }

  // Event emitter
  emit(eventName, data) {
    if (this.eventListeners && this.eventListeners[eventName]) {
      this.eventListeners[eventName].forEach(callback => callback(data))
    }
  }

  // Event listener management
  on(eventName, callback) {
    if (!this.eventListeners) {
      this.eventListeners = {}
    }
    if (!this.eventListeners[eventName]) {
      this.eventListeners[eventName] = []
    }
    this.eventListeners[eventName].push(callback)
  }

  off(eventName, callback) {
    if (this.eventListeners && this.eventListeners[eventName]) {
      const index = this.eventListeners[eventName].indexOf(callback)
      if (index > -1) {
        this.eventListeners[eventName].splice(index, 1)
      }
    }
  }
}
