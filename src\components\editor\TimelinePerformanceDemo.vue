<template>
  <div class="timeline-performance-demo p-6 bg-gray-900 text-white">
    <h2 class="text-2xl font-bold mb-6">Timeline Performance Demo</h2>
    
    <!-- Test Controls -->
    <div class="controls mb-6 space-y-4">
      <div class="flex items-center gap-4">
        <label class="text-sm font-medium">Item Count:</label>
        <select v-model="selectedItemCount" class="bg-gray-800 border border-gray-600 rounded px-3 py-1">
          <option v-for="count in itemCounts" :key="count" :value="count">
            {{ count }} items
          </option>
        </select>
        
        <button 
          @click="runTest" 
          :disabled="testing"
          class="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 px-4 py-2 rounded text-sm font-medium"
        >
          {{ testing ? 'Testing...' : 'Run Performance Test' }}
        </button>
        
        <button 
          @click="runCompleteTest" 
          :disabled="testing"
          class="bg-green-600 hover:bg-green-700 disabled:bg-gray-600 px-4 py-2 rounded text-sm font-medium"
        >
          {{ testing ? 'Testing...' : 'Run Complete Test Suite' }}
        </button>
      </div>
      
      <div class="flex items-center gap-4">
        <label class="text-sm font-medium">Demo Mode:</label>
        <label class="flex items-center gap-2">
          <input type="radio" v-model="demoMode" value="dom" class="text-blue-600">
          <span>DOM-based (Original)</span>
        </label>
        <label class="flex items-center gap-2">
          <input type="radio" v-model="demoMode" value="canvas" class="text-blue-600">
          <span>Canvas-based (Optimized)</span>
        </label>
      </div>
    </div>

    <!-- Performance Results -->
    <div v-if="lastResult" class="results mb-6 p-4 bg-gray-800 rounded">
      <h3 class="text-lg font-semibold mb-3">Performance Results ({{ lastResult.itemCount }} items)</h3>
      
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
        <div class="metric">
          <div class="text-sm text-gray-400">DOM Total Time</div>
          <div class="text-xl font-mono">{{ lastResult.dom.totalTime.toFixed(2) }}ms</div>
        </div>
        <div class="metric">
          <div class="text-sm text-gray-400">Canvas Total Time</div>
          <div class="text-xl font-mono">{{ lastResult.canvas.totalTime.toFixed(2) }}ms</div>
        </div>
        <div class="metric">
          <div class="text-sm text-gray-400">Speed Improvement</div>
          <div class="text-xl font-mono text-green-400">{{ lastResult.comparison.speedImprovement.total }}</div>
        </div>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <h4 class="font-medium mb-2">Detailed Breakdown</h4>
          <div class="space-y-1 text-sm">
            <div class="flex justify-between">
              <span>Scroll Performance:</span>
              <span class="text-green-400">{{ lastResult.comparison.speedImprovement.scroll }} faster</span>
            </div>
            <div class="flex justify-between">
              <span>Selection Performance:</span>
              <span class="text-green-400">{{ lastResult.comparison.speedImprovement.selection }} faster</span>
            </div>
            <div class="flex justify-between">
              <span>Memory Usage:</span>
              <span class="text-green-400">{{ lastResult.comparison.memoryImprovement }} less</span>
            </div>
          </div>
        </div>
        
        <div>
          <h4 class="font-medium mb-2">Canvas Optimizations</h4>
          <div class="space-y-1 text-sm">
            <div class="flex justify-between">
              <span>Visible Items Rendered:</span>
              <span>{{ lastResult.comparison.canvasSpecificMetrics.visibleItemsRendered }}</span>
            </div>
            <div class="flex justify-between">
              <span>Viewport Culling:</span>
              <span class="text-blue-400">{{ lastResult.comparison.canvasSpecificMetrics.visibleItemsPercentage }}</span>
            </div>
            <div class="flex justify-between">
              <span>Item Render Time:</span>
              <span>{{ lastResult.comparison.canvasSpecificMetrics.itemRenderTime }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Complete Test Results -->
    <div v-if="completeResults.length > 0" class="complete-results mb-6">
      <h3 class="text-lg font-semibold mb-3">Complete Test Suite Results</h3>
      <div class="overflow-x-auto">
        <table class="w-full text-sm">
          <thead>
            <tr class="border-b border-gray-600">
              <th class="text-left p-2">Item Count</th>
              <th class="text-left p-2">DOM Time (ms)</th>
              <th class="text-left p-2">Canvas Time (ms)</th>
              <th class="text-left p-2">Speed Improvement</th>
              <th class="text-left p-2">Memory Improvement</th>
              <th class="text-left p-2">Viewport Culling</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="result in completeResults" :key="result.itemCount" class="border-b border-gray-700">
              <td class="p-2 font-mono">{{ result.itemCount }}</td>
              <td class="p-2 font-mono">{{ result.dom.totalTime.toFixed(2) }}</td>
              <td class="p-2 font-mono">{{ result.canvas.totalTime.toFixed(2) }}</td>
              <td class="p-2 font-mono text-green-400">{{ result.comparison.speedImprovement.total }}</td>
              <td class="p-2 font-mono text-green-400">{{ result.comparison.memoryImprovement }}</td>
              <td class="p-2 font-mono text-blue-400">{{ result.comparison.canvasSpecificMetrics.visibleItemsPercentage }}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- Demo Timeline Container -->
    <div class="demo-container">
      <h3 class="text-lg font-semibold mb-3">
        Live Demo - {{ demoMode === 'dom' ? 'DOM-based' : 'Canvas-based' }} Timeline
        <span class="text-sm text-gray-400">({{ demoItems.length }} items)</span>
      </h3>
      
      <div class="demo-controls mb-3 flex items-center gap-4">
        <button @click="generateDemoData" class="bg-gray-600 hover:bg-gray-700 px-3 py-1 rounded text-sm">
          Generate New Data
        </button>
        <button @click="toggleDemo" class="bg-purple-600 hover:bg-purple-700 px-3 py-1 rounded text-sm">
          Switch to {{ demoMode === 'dom' ? 'Canvas' : 'DOM' }}
        </button>
      </div>

      <div 
        ref="demoContainer" 
        class="demo-timeline w-full h-20 bg-gray-800 border border-gray-600 rounded overflow-auto relative"
      >
        <!-- Demo timeline will be rendered here -->
      </div>
      
      <div class="demo-stats mt-2 text-xs text-gray-400">
        <span>Scroll to see performance difference • </span>
        <span>{{ demoMode === 'canvas' ? 'Canvas optimizations active' : 'All DOM elements rendered' }}</span>
      </div>
    </div>

    <!-- Loading Indicator -->
    <div v-if="testing" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-gray-800 p-6 rounded-lg">
        <div class="flex items-center gap-3">
          <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
          <span>Running performance tests...</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import { TimelinePerformanceTest } from '@/utils/timelinePerformanceTest.js'

// Reactive data
const testing = ref(false)
const selectedItemCount = ref(2000)
const itemCounts = [500, 1000, 2000, 5000, 10000]
const lastResult = ref(null)
const completeResults = ref([])
const demoMode = ref('canvas')
const demoItems = ref([])
const demoContainer = ref(null)

let performanceTest = null
let demoRenderer = null

// Initialize
onMounted(() => {
  performanceTest = new TimelinePerformanceTest()
  generateDemoData()
  setupDemo()
})

onUnmounted(() => {
  if (demoRenderer) {
    demoRenderer.destroy()
  }
})

// Methods
const runTest = async () => {
  if (testing.value) return
  
  testing.value = true
  try {
    const items = performanceTest.generateTestData(selectedItemCount.value)
    
    // Create temporary container
    const container = document.createElement('div')
    container.style.width = '1000px'
    container.style.height = '80px'
    container.style.position = 'relative'
    container.style.overflow = 'hidden'
    document.body.appendChild(container)

    try {
      await performanceTest.testDOMPerformance(items, container)
      container.innerHTML = ''
      await performanceTest.testCanvasPerformance(items, container)
      
      lastResult.value = {
        itemCount: selectedItemCount.value,
        dom: performanceTest.results.dom,
        canvas: performanceTest.results.canvas,
        comparison: performanceTest.compareResults()
      }
    } finally {
      document.body.removeChild(container)
    }
  } catch (error) {
    console.error('Test failed:', error)
  } finally {
    testing.value = false
  }
}

const runCompleteTest = async () => {
  if (testing.value) return
  
  testing.value = true
  try {
    const results = await performanceTest.runCompleteTest([500, 1000, 2000, 5000])
    completeResults.value = results
    performanceTest.generateReport(results)
  } catch (error) {
    console.error('Complete test failed:', error)
  } finally {
    testing.value = false
  }
}

const generateDemoData = () => {
  demoItems.value = performanceTest.generateTestData(1000)
  setupDemo()
}

const setupDemo = async () => {
  if (!demoContainer.value) return
  
  await nextTick()
  
  if (demoRenderer) {
    demoRenderer.destroy()
    demoRenderer = null
  }
  
  demoContainer.value.innerHTML = ''
  
  if (demoMode.value === 'canvas') {
    const { CanvasTimelineRenderer } = await import('../editor/CanvasTimelineRenderer.js')
    demoRenderer = new CanvasTimelineRenderer(demoContainer.value, {
      trackHeight: 80,
      pixelsPerSecond: 50,
      zoom: 0.5
    })
    demoRenderer.setItems(demoItems.value)
    demoRenderer.render()
  } else {
    // Create DOM-based demo (simplified)
    demoItems.value.slice(0, 200).forEach(item => { // Limit for demo
      const el = document.createElement('div')
      el.className = 'absolute bg-blue-600 border border-blue-500 rounded text-xs p-1 cursor-pointer'
      el.style.left = `${item.startTime * 50}px`
      el.style.width = `${(item.endTime - item.startTime) * 50}px`
      el.style.height = '60px'
      el.style.top = '10px'
      el.textContent = item.id
      demoContainer.value.appendChild(el)
    })
  }
}

const toggleDemo = () => {
  demoMode.value = demoMode.value === 'dom' ? 'canvas' : 'dom'
  setupDemo()
}
</script>

<style scoped>
.demo-timeline {
  min-width: 2000px;
}

.metric {
  text-align: center;
  padding: 1rem;
  background: rgba(59, 130, 246, 0.1);
  border-radius: 0.5rem;
}
</style>
