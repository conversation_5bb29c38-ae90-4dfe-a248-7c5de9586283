<template>
  <div
    ref="canvasContainer"
    class="canvas-timeline-track absolute inset-0"
  >
    <!-- Canvas elements will be inserted here by CanvasTimelineRenderer -->
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { useTimelineStore } from '@/stores/timeline-store'
import { useTTSStore } from '@/stores/ttsStore'
import { CanvasTimelineRenderer } from './CanvasTimelineRenderer.js'
import { parseTimeToSeconds } from '@/lib/utils'

// Stores
const timelineStore = useTimelineStore()
const ttsStore = useTTSStore()

// Refs
const canvasContainer = ref(null)
let renderer = null

// Emits - keep same API as original TimelineTrack
const emit = defineEmits([
  'context-menu',
  'item-action'
])

// Computed - same as original TimelineTrack
const subtitleItems = computed(() => {
  return timelineStore.subtitleItems.map(item => ({
    ...item,
    startTime: item.startTime || parseTimeToSeconds(item.start),
    endTime: item.endTime || parseTimeToSeconds(item.end),
    hasAudio: !!(item.audioUrl || item.audioUrl1 || item.audioUrl2 || item.audioUrl3)
  }))
})

// Initialize renderer
const initRenderer = async () => {
  if (!canvasContainer.value || renderer) return

  await nextTick()

  renderer = new CanvasTimelineRenderer(canvasContainer.value, {
    trackHeight: timelineStore.trackHeight,
    pixelsPerSecond: 100,
    zoom: timelineStore.zoom
  })

  // Set up event listeners
  setupRendererEvents()

  // Initial render
  updateRenderer()
}

const setupRendererEvents = () => {
  if (!renderer) return

  // Drag events
  renderer.on('drag-start', (data) => {
    timelineStore.setDragState(true, 'move')
    timelineStore.setDragStartTime(data.items[0]?.originalStartTime || 0)
  })

  renderer.on('drag', (data) => {
    // Update items in store through throttled updates
    throttledDragUpdate(data.deltaTime)
  })

  renderer.on('drag-end', (data) => {
    timelineStore.setDragState(false)
    // Save final positions to store
    updateItemsInStore()
  })

  // Resize events
  renderer.on('resize-start', (data) => {
    timelineStore.setDragState(true, `resize-${data.direction}`)
  })

  renderer.on('resize', (data) => {
    // Update item in store
    updateItemsInStore()
  })

  renderer.on('resize-end', (data) => {
    timelineStore.setDragState(false)
    updateItemsInStore()
  })

  // Selection events
  renderer.on('selection-changed', (data) => {
    timelineStore.setSelectedItems(data.selectedItems)
  })

  // Audio events
  renderer.on('audio-toggle', (data) => {
    const action = data.item.isPlaying ? 'audio-pause' : 'audio-play'
    emit('item-action', action, data.item)
  })

  renderer.on('audio-seek', (data) => {
    emit('item-action', 'audio-seek', {
      item: data.item,
      progress: data.progress
    })
  })

  renderer.on('voice-select', (data) => {
    emit('item-action', 'voice-select', {
      item: data.item,
      voiceIndex: data.voiceIndex
    })
  })

  // Context menu events
  renderer.on('context-menu', (data) => {
    emit('context-menu', data.event, data.time)
  })
}

// Throttled drag update to prevent performance issues
const throttledDragUpdate = (() => {
  let rafId = null
  return (deltaTime) => {
    if (rafId) return
    
    rafId = requestAnimationFrame(() => {
      // Update selected items timing
      timelineStore.selectedItems.forEach(itemId => {
        const item = subtitleItems.value.find(i => i.id === itemId)
        if (item) {
          const newStartTime = Math.max(0, timelineStore.dragStartTime + deltaTime)
          const duration = item.endTime - item.startTime
          
          // Check for overlaps and update if valid
          if (!checkOverlap(itemId, newStartTime, newStartTime + duration)) {
            updateItemTiming(item, newStartTime, newStartTime + duration)
          }
        }
      })
      
      rafId = null
    })
  }
})()

// Update renderer when data changes
const updateRenderer = () => {
  if (!renderer) return

  renderer.setItems(subtitleItems.value)
  renderer.setSelectedItems(timelineStore.selectedItems)
  renderer.setZoom(timelineStore.zoom)
  renderer.render()
}

// Update items in TTS store
const updateItemsInStore = () => {
  const updatedItems = [...ttsStore.currentSrtList.items]
  
  subtitleItems.value.forEach(item => {
    const index = updatedItems.findIndex(i => i.id === item.id)
    if (index !== -1) {
      updatedItems[index] = {
        ...updatedItems[index],
        startTime: item.startTime,
        endTime: item.endTime,
        start: secondsToSRTTime(item.startTime),
        end: secondsToSRTTime(item.endTime)
      }
    }
  })

  ttsStore.currentSrtList.items = updatedItems
}

// Helper functions (same as original TimelineTrack)
const checkOverlap = (excludeId, startTime, endTime) => {
  return subtitleItems.value.some(item => {
    if (item.id === excludeId) return false
    return !(endTime <= item.startTime || startTime >= item.endTime)
  })
}

const updateItemTiming = (item, startTime, endTime) => {
  item.startTime = startTime
  item.endTime = endTime
  
  // Update in renderer
  if (renderer) {
    renderer.markDirty('items')
    renderer.render()
  }
}

const secondsToSRTTime = (seconds) => {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = Math.floor(seconds % 60)
  const ms = Math.floor((seconds % 1) * 1000)
  
  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')},${ms.toString().padStart(3, '0')}`
}

// Handle scroll updates
const handleScroll = (scrollLeft) => {
  if (renderer) {
    renderer.setScrollPosition(scrollLeft)
  }
}

// Watchers
watch(subtitleItems, () => {
  updateRenderer()
}, { deep: true })

watch(() => timelineStore.selectedItems, () => {
  if (renderer) {
    renderer.setSelectedItems(timelineStore.selectedItems)
    renderer.render()
  }
}, { deep: true })

watch(() => timelineStore.zoom, () => {
  if (renderer) {
    renderer.setZoom(timelineStore.zoom)
    renderer.render()
  }
})

watch(() => timelineStore.scrollLeft, (newScrollLeft) => {
  handleScroll(newScrollLeft)
})

// Lifecycle
onMounted(() => {
  initRenderer()
})

onUnmounted(() => {
  if (renderer) {
    renderer.destroy()
    renderer = null
  }
})

// Expose methods for parent component compatibility
defineExpose({
  updateRenderer,
  handleScroll
})
</script>

<style scoped>
.canvas-timeline-track {
  /* Ensure canvas container has proper positioning */
  position: relative;
  overflow: hidden;
}

/* Canvas styling will be handled by CanvasTimelineRenderer */
:deep(.timeline-canvas-background),
:deep(.timeline-canvas-items),
:deep(.timeline-canvas-overlay) {
  position: absolute;
  top: 0;
  left: 0;
  pointer-events: none;
}

:deep(.timeline-canvas-overlay) {
  pointer-events: auto; /* Only overlay canvas handles events */
}
</style>
