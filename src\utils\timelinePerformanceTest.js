/**
 * Timeline Performance Testing Utility
 * Compare performance between DOM-based and Canvas-based timeline rendering
 */

export class TimelinePerformanceTest {
  constructor() {
    this.results = {
      dom: {},
      canvas: {},
      comparison: {}
    }
  }

  // Generate test dataset with specified number of items
  generateTestData(itemCount = 2000) {
    const items = []
    const duration = itemCount * 2 // 2 seconds per item on average
    
    for (let i = 0; i < itemCount; i++) {
      const startTime = (i * duration) / itemCount + Math.random() * 0.5
      const itemDuration = 1 + Math.random() * 3 // 1-4 seconds duration
      
      items.push({
        id: `item-${i}`,
        startTime,
        endTime: startTime + itemDuration,
        text: `Test subtitle ${i} with some longer text content to simulate real usage`,
        translatedText: `Translated test subtitle ${i} with longer content`,
        isEnabled: true,
        isVoice: Math.floor(Math.random() * 5) + 1,
        hasAudio: Math.random() > 0.3,
        isGenerated1: Math.random() > 0.5,
        isGenerated2: Math.random() > 0.7,
        isGenerated3: Math.random() > 0.8,
        isGenerated4: Math.random() > 0.9,
        isGenerated5: Math.random() > 0.95,
        audioUrl1: Math.random() > 0.5 ? 'test-audio-1.mp3' : '',
        audioUrl2: Math.random() > 0.7 ? 'test-audio-2.mp3' : '',
        audioUrl3: Math.random() > 0.8 ? 'test-audio-3.mp3' : '',
        audioDuration: 1 + Math.random() * 3,
        isPlaying: false,
        audioProgress: 0,
        waveformData: this.generateWaveformData()
      })
    }

    return items.sort((a, b) => a.startTime - b.startTime)
  }

  generateWaveformData() {
    const samples = 100 + Math.floor(Math.random() * 200)
    const data = []
    
    for (let i = 0; i < samples; i++) {
      data.push((Math.random() - 0.5) * 2) // -1 to 1 range
    }
    
    return data
  }

  // Test DOM-based rendering performance
  async testDOMPerformance(items, container) {
    console.log('Testing DOM-based timeline performance...')
    
    const startTime = performance.now()
    let renderCount = 0
    
    // Simulate DOM element creation for each item
    const elements = []
    
    items.forEach(item => {
      // Main item element
      const itemEl = document.createElement('div')
      itemEl.className = 'timeline-item absolute cursor-pointer select-none'
      itemEl.style.left = `${item.startTime * 100}px`
      itemEl.style.width = `${(item.endTime - item.startTime) * 100}px`
      itemEl.style.height = '70px'
      itemEl.style.top = '5px'
      
      // Resize handles
      const leftHandle = document.createElement('div')
      leftHandle.className = 'resize-handle resize-left'
      itemEl.appendChild(leftHandle)
      
      const rightHandle = document.createElement('div')
      rightHandle.className = 'resize-handle resize-right'
      itemEl.appendChild(rightHandle)
      
      // Content
      const content = document.createElement('div')
      content.className = 'item-content'
      content.innerHTML = `
        <div class="flex items-center justify-between text-xs mb-1">
          <span>${item.id}</span>
          <span>${(item.endTime - item.startTime).toFixed(1)}s</span>
        </div>
        <div class="text-xs text-gray-200">${item.text}</div>
      `
      itemEl.appendChild(content)
      
      // Audio controls if has audio
      if (item.hasAudio) {
        const audioControls = document.createElement('div')
        audioControls.className = 'audio-controls'
        audioControls.innerHTML = `
          <button class="audio-play-btn">▶</button>
          <canvas class="waveform-canvas" width="100" height="20"></canvas>
        `
        content.appendChild(audioControls)
      }
      
      elements.push(itemEl)
      container.appendChild(itemEl)
      renderCount++
    })

    const domCreationTime = performance.now() - startTime

    // Test scroll performance
    const scrollStart = performance.now()
    for (let i = 0; i < 100; i++) {
      container.scrollLeft = i * 10
      // Force reflow
      container.offsetHeight
    }
    const scrollTime = performance.now() - scrollStart

    // Test selection performance
    const selectionStart = performance.now()
    elements.slice(0, 100).forEach(el => {
      el.classList.add('selected')
    })
    const selectionTime = performance.now() - selectionStart

    // Cleanup
    elements.forEach(el => el.remove())

    this.results.dom = {
      itemCount: items.length,
      domCreationTime,
      scrollTime,
      selectionTime,
      totalTime: domCreationTime + scrollTime + selectionTime,
      memoryUsage: this.getMemoryUsage()
    }

    console.log('DOM Performance Results:', this.results.dom)
    return this.results.dom
  }

  // Test Canvas-based rendering performance
  async testCanvasPerformance(items, container) {
    console.log('Testing Canvas-based timeline performance...')
    
    // Import CanvasTimelineRenderer dynamically
    const { CanvasTimelineRenderer } = await import('../components/editor/CanvasTimelineRenderer.js')
    
    const startTime = performance.now()
    
    // Create renderer
    const renderer = new CanvasTimelineRenderer(container, {
      trackHeight: 80,
      pixelsPerSecond: 100,
      zoom: 1,
      debug: true
    })

    const rendererCreationTime = performance.now() - startTime

    // Test initial render
    const renderStart = performance.now()
    renderer.setItems(items)
    renderer.render()
    const initialRenderTime = performance.now() - renderStart

    // Test scroll performance
    const scrollStart = performance.now()
    for (let i = 0; i < 100; i++) {
      renderer.setScrollPosition(i * 10)
    }
    const scrollTime = performance.now() - scrollStart

    // Test selection performance
    const selectionStart = performance.now()
    const selectedIds = items.slice(0, 100).map(item => item.id)
    renderer.setSelectedItems(selectedIds)
    renderer.render()
    const selectionTime = performance.now() - selectionStart

    // Test zoom performance
    const zoomStart = performance.now()
    for (let zoom = 0.5; zoom <= 2; zoom += 0.1) {
      renderer.setZoom(zoom)
      renderer.render()
    }
    const zoomTime = performance.now() - zoomStart

    // Cleanup
    renderer.destroy()

    this.results.canvas = {
      itemCount: items.length,
      rendererCreationTime,
      initialRenderTime,
      scrollTime,
      selectionTime,
      zoomTime,
      totalTime: rendererCreationTime + initialRenderTime + scrollTime + selectionTime + zoomTime,
      memoryUsage: this.getMemoryUsage(),
      lastVisibleItemCount: renderer.lastVisibleItemCount,
      lastItemRenderTime: renderer.lastItemRenderTime
    }

    console.log('Canvas Performance Results:', this.results.canvas)
    return this.results.canvas
  }

  // Compare performance results
  compareResults() {
    const dom = this.results.dom
    const canvas = this.results.canvas

    if (!dom || !canvas) {
      console.error('Both DOM and Canvas tests must be run before comparison')
      return null
    }

    this.results.comparison = {
      itemCount: dom.itemCount,
      speedImprovement: {
        total: ((dom.totalTime - canvas.totalTime) / dom.totalTime * 100).toFixed(1) + '%',
        scroll: ((dom.scrollTime - canvas.scrollTime) / dom.scrollTime * 100).toFixed(1) + '%',
        selection: ((dom.selectionTime - canvas.selectionTime) / dom.selectionTime * 100).toFixed(1) + '%'
      },
      memoryImprovement: ((dom.memoryUsage - canvas.memoryUsage) / dom.memoryUsage * 100).toFixed(1) + '%',
      canvasSpecificMetrics: {
        visibleItemsRendered: canvas.lastVisibleItemCount,
        visibleItemsPercentage: ((canvas.lastVisibleItemCount / canvas.itemCount) * 100).toFixed(1) + '%',
        itemRenderTime: canvas.lastItemRenderTime?.toFixed(2) + 'ms'
      }
    }

    console.log('Performance Comparison:', this.results.comparison)
    return this.results.comparison
  }

  // Get memory usage (approximate)
  getMemoryUsage() {
    if (performance.memory) {
      return performance.memory.usedJSHeapSize
    }
    return 0
  }

  // Run complete performance test
  async runCompleteTest(itemCounts = [500, 1000, 2000, 5000]) {
    const results = []

    for (const itemCount of itemCounts) {
      console.log(`\n=== Testing with ${itemCount} items ===`)
      
      const items = this.generateTestData(itemCount)
      
      // Create test container
      const container = document.createElement('div')
      container.style.width = '1000px'
      container.style.height = '80px'
      container.style.position = 'relative'
      container.style.overflow = 'hidden'
      document.body.appendChild(container)

      try {
        // Test DOM performance
        await this.testDOMPerformance(items, container)
        
        // Clear container
        container.innerHTML = ''
        
        // Test Canvas performance
        await this.testCanvasPerformance(items, container)
        
        // Compare results
        const comparison = this.compareResults()
        
        results.push({
          itemCount,
          dom: this.results.dom,
          canvas: this.results.canvas,
          comparison
        })

      } catch (error) {
        console.error(`Error testing ${itemCount} items:`, error)
      } finally {
        // Cleanup
        document.body.removeChild(container)
      }

      // Reset results for next test
      this.results = { dom: {}, canvas: {}, comparison: {} }
    }

    return results
  }

  // Generate performance report
  generateReport(results) {
    console.log('\n=== TIMELINE PERFORMANCE REPORT ===')
    console.table(results.map(r => ({
      'Item Count': r.itemCount,
      'DOM Total (ms)': r.dom.totalTime.toFixed(2),
      'Canvas Total (ms)': r.canvas.totalTime.toFixed(2),
      'Speed Improvement': r.comparison.speedImprovement.total,
      'Memory Improvement': r.comparison.memoryImprovement,
      'Visible Items %': r.comparison.canvasSpecificMetrics.visibleItemsPercentage
    })))

    return results
  }
}

// Export convenience function
export async function runTimelinePerformanceTest() {
  const tester = new TimelinePerformanceTest()
  const results = await tester.runCompleteTest()
  return tester.generateReport(results)
}
