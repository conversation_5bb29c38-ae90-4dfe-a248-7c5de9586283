<template>
  <div
    class="timeline-track absolute inset-0"
  >
    <!-- Grid lines -->
    <div class="absolute inset-0 pointer-events-none">
      <div
        v-for="gridLine in gridLines"
        :key="gridLine"
        class="absolute top-0 bottom-0 w-px bg-gray-700 opacity-30"
        :style="{ left: gridLine + 'px' }"
      ></div>
    </div>

    <!-- Subtitle Items -->
    <TimelineItem
      v-for="item in subtitleItems"
      :key="item.id"
      :item="item"
      @select="handleItemSelect"
      @drag-start="handleDragStart"
      @drag="handleDrag"
      @drag-end="handleDragEnd"
      @resize-start="handleResizeStart"
      @resize="handleResize"
      @resize-end="handleResizeEnd"
      @context-menu="handleContextMenu"
      @audio-play="handleAudioPlay"
      @audio-pause="handleAudioPause"
      @seek-to-time="handleSeekToTime"
    />

    <!-- Selection Rectangle -->
    <div
      v-if="isSelecting"
      class="absolute border-2 border-blue-500 bg-blue-500 bg-opacity-50 pointer-events-none z-10"
      :style="selectionRectStyle"
    ></div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useTimelineStore } from '@/stores/timeline-store'
import { useTTSStore } from '@/stores/ttsStore'
import { parseTimeToSeconds } from '@/lib/utils'
import { secondsToSRTTime } from '@/lib/subtitleUtils'
import { state } from '@/lib/state'
import TimelineItem from './TimelineItem.vue'

const emit = defineEmits(['context-menu', 'item-action'])

const timelineStore = useTimelineStore()
const ttsStore = useTTSStore()

// Selection state
const isSelecting = ref(false)
const selectionStart = ref({ x: 0, y: 0 })
const selectionEnd = ref({ x: 0, y: 0 })

// Computed
const subtitleItems = computed(() => {
  return timelineStore.subtitleItems.map(item => ({
    ...item,
    startTime: item.startTime || parseTimeToSeconds(item.start),
    endTime: item.endTime || parseTimeToSeconds(item.end)
  }))
})

const gridLines = computed(() => {
  const lines = []
  const gridSize = timelineStore.gridSize
  const duration = timelineStore.duration

  for (let time = 0; time <= duration; time += gridSize) {
    lines.push(timelineStore.timeToPixel(time))
  }

  return lines
})

const selectionRectStyle = computed(() => {
  const left = Math.min(selectionStart.value.x, selectionEnd.value.x)
  const top = Math.min(selectionStart.value.y, selectionEnd.value.y)
  const width = Math.abs(selectionEnd.value.x - selectionStart.value.x)
  const height = Math.abs(selectionEnd.value.y - selectionStart.value.y)

  return {
    left: left + 'px',
    top: top + 'px',
    width: width + 'px',
    height: height + 'px'
  }
})

// Methods
const handleItemSelect = (item, event) => {
  console.log('handleItemSelect', item, event)
  if (event.ctrlKey || event.metaKey) {
    // Multi-select
    timelineStore.toggleSelection(item.id)
  } else if (event.shiftKey && timelineStore.selectedItems.length > 0) {
    // Range select
    const lastSelectedId = timelineStore.selectedItems[timelineStore.selectedItems.length - 1]
    const lastSelectedIndex = subtitleItems.value.findIndex(i => i.id === lastSelectedId)
    const currentIndex = subtitleItems.value.findIndex(i => i.id === item.id)

    const startIndex = Math.min(lastSelectedIndex, currentIndex)
    const endIndex = Math.max(lastSelectedIndex, currentIndex)

    const rangeIds = subtitleItems.value
      .slice(startIndex, endIndex + 1)
      .map(i => i.id)

    timelineStore.selectMultiple(rangeIds)
  } else {
    // Single select
    timelineStore.clearSelection()
    timelineStore.selectItem(item.id)
  }
}

const handleDragStart = (item, event) => {
  timelineStore.saveState()

  if (!timelineStore.isItemSelected(item.id)) {
    timelineStore.clearSelection()
    timelineStore.selectItem(item.id)
  }

  const startTime = item.startTime
  timelineStore.startDrag('move', item, event.clientX, startTime)
}

// Throttle function using requestAnimationFrame for better performance
const throttleRAF = (func) => {
  let rafId = null
  let lastArgs = null

  return function (...args) {
    lastArgs = args
    if (rafId === null) {
      rafId = requestAnimationFrame(() => {
        func.apply(this, lastArgs)
        rafId = null
      })
    }
  }
}

const handleDrag = (deltaX) => {
  if (!timelineStore.isDragging || timelineStore.dragType !== 'move') return

  // Use throttled update for better performance
  throttledDragUpdate(deltaX)
}

const throttledDragUpdate = throttleRAF((deltaX) => {
  const deltaTime = timelineStore.pixelToTime(deltaX)
  const newStartTime = Math.max(0, timelineStore.dragStartTime + deltaTime)

  // Snap to grid
  const snappedStartTime = timelineStore.snapToGrid ?
    timelineStore.getSnapTime(newStartTime) : newStartTime

  // Update all selected items
  timelineStore.selectedItems.forEach(itemId => {
    const item = subtitleItems.value.find(i => i.id === itemId)
    if (item) {
      const duration = item.endTime - item.startTime
      const newEndTime = snappedStartTime + duration

      // Check for overlaps with other items
      if (!checkOverlap(itemId, snappedStartTime, newEndTime)) {
        updateItemTiming(item, snappedStartTime, newEndTime)
      }
    }
  })
})

const handleDragEnd = () => {
  timelineStore.stopDrag()
}

const handleResizeStart = (item, edge, event) => {
  timelineStore.saveState()

  const resizeType = edge === 'left' ? 'resize-left' : 'resize-right'
  const startTime = edge === 'left' ? item.startTime : item.endTime

  timelineStore.startDrag(resizeType, item, event.clientX, startTime)
}

const handleResize = (deltaX) => {
  if (!timelineStore.isDragging || !timelineStore.dragType.startsWith('resize')) return

  // Use throttled update for better performance
  throttledResizeUpdate(deltaX)
}

const throttledResizeUpdate = throttleRAF((deltaX) => {
  const deltaTime = timelineStore.pixelToTime(deltaX)
  const item = timelineStore.dragItem

  if (timelineStore.dragType === 'resize-left') {
    const newStartTime = Math.max(0, timelineStore.dragStartTime + deltaTime)
    const snappedStartTime = timelineStore.snapToGrid ?
      timelineStore.getSnapTime(newStartTime) : newStartTime

    // Ensure minimum duration
    if (item.endTime - snappedStartTime >= 0.1) {
      updateItemTiming(item, snappedStartTime, item.endTime)
    }
  } else if (timelineStore.dragType === 'resize-right') {
    const newEndTime = Math.max(item.startTime + 0.1, timelineStore.dragStartTime + deltaTime)
    const snappedEndTime = timelineStore.snapToGrid ?
      timelineStore.getSnapTime(newEndTime) : newEndTime

    updateItemTiming(item, item.startTime, snappedEndTime)
  }
})

const handleResizeEnd = () => {
  timelineStore.stopDrag()
}

const handleContextMenu = (event) => {
  emit('context-menu', event)
}

// Audio playback handlers
const handleAudioPlay = (item) => {
  console.log('Audio playing:', item.id)
  // Update global state to indicate which subtitle is playing audio
  state.currentPlayingSubtitleId = item.id

  // Emit to parent for any additional handling
  emit('item-action', 'audio-play', item)
}

const handleAudioPause = (item) => {
  console.log('Audio paused:', item.id)
  // Clear global playing state if this was the playing item
  if (state.currentPlayingSubtitleId === item.id) {
    state.currentPlayingSubtitleId = null
  }

  // Emit to parent for any additional handling
  emit('item-action', 'audio-pause', item)
}

const handleSeekToTime = (time) => {
  console.log('Seeking to time:', time)
  // Update timeline position
  timelineStore.setCurrentTime(time)

  // Emit to parent for video synchronization
  emit('item-action', 'seek-to-time', { time })
}

const checkOverlap = (excludeId, startTime, endTime) => {
  return subtitleItems.value.some(item => {
    if (item.id === excludeId) return false
    return !(endTime <= item.startTime || startTime >= item.endTime)
  })
}

const updateItemTiming = (item, startTime, endTime) => {
  // Update the item in the store
  const ttsItems = ttsStore.currentSrtList.items
  const index = ttsItems.findIndex(i => i.id === item.id)

  if (index !== -1) {
    ttsItems[index] = {
      ...ttsItems[index],
      start: secondsToSRTTime(startTime),
      end: secondsToSRTTime(endTime),
      startTime,
      endTime
    }
  }
}

// Selection rectangle
const startSelection = (event) => {
  if (event.target !== event.currentTarget) return

  isSelecting.value = true
  const rect = event.currentTarget.getBoundingClientRect()
  selectionStart.value = {
    x: event.clientX - rect.left,
    y: event.clientY - rect.top
  }
  selectionEnd.value = { ...selectionStart.value }

  if (!event.ctrlKey && !event.metaKey) {
    timelineStore.clearSelection()
  }

  document.addEventListener('mousemove', updateSelection)
  document.addEventListener('mouseup', endSelection)
}

const updateSelection = (event) => {
  if (!isSelecting.value) return

  const timelineElement = document.querySelector('.timeline-track')
  if (!timelineElement) return

  const rect = timelineElement.getBoundingClientRect()
  selectionEnd.value = {
    x: event.clientX - rect.left,
    y: event.clientY - rect.top
  }
}

const endSelection = () => {
  if (isSelecting.value) {
    // Select items within selection rectangle
    const left = Math.min(selectionStart.value.x, selectionEnd.value.x)
    const right = Math.max(selectionStart.value.x, selectionEnd.value.x)
    const top = Math.min(selectionStart.value.y, selectionEnd.value.y)
    const bottom = Math.max(selectionStart.value.y, selectionEnd.value.y)

    const selectedIds = subtitleItems.value
      .filter(item => {
        const itemLeft = timelineStore.timeToPixel(item.startTime)
        const itemRight = timelineStore.timeToPixel(item.endTime)
        const itemTop = 0
        const itemBottom = timelineStore.trackHeight

        return !(itemRight < left || itemLeft > right || itemBottom < top || itemTop > bottom)
      })
      .map(item => item.id)

    timelineStore.selectMultiple(selectedIds)
  }

  isSelecting.value = false
  document.removeEventListener('mousemove', updateSelection)
  document.removeEventListener('mouseup', endSelection)
}

// Lifecycle
onMounted(() => {
  const trackElement = document.querySelector('.timeline-track')
  if (trackElement) {
    trackElement.addEventListener('mousedown', startSelection)
    trackElement.addEventListener('contextmenu', (event) => {
      if (event.target === trackElement) {
        emit('context-menu', event)
      }
    })

    onUnmounted(() => {
      trackElement.removeEventListener('mousedown', startSelection)
      document.removeEventListener('mousemove', updateSelection)
      document.removeEventListener('mouseup', endSelection)
    })
  }
})

/*
    :style="{
      height: timelineStore.trackHeight + 'px',
      width: timelineStore.totalTimelineWidth + 'px',
      transform: `translateX(-${timelineStore.scrollLeft}px)`
    }"*/

</script>

<style scoped>
.timeline-track {
  background: linear-gradient(to bottom, #1f2937 0%, #111827 100%);
  border-bottom: 1px solid #374151;
}
</style>
